<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="16dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardBackgroundColor="@color/dark_gray"
            app:cardCornerRadius="8dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:orientation="horizontal"
                    android:gravity="center_vertical">

                    <!-- Výběr třídy -->
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/select_class"
                        android:textColor="@android:color/white"
                        android:textSize="16sp"
                        android:textStyle="bold" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="     "
                        android:textColor="@android:color/white"
                        android:textSize="16sp"
                        android:textStyle="bold" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/class_b"
                        android:textColor="@android:color/white"
                        android:textSize="14sp" />

                    <Switch
                        android:id="@+id/switch_class"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="16dp"
                        android:layout_marginEnd="16dp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/class_a"
                        android:textColor="@android:color/white"
                        android:textSize="14sp" />
                </LinearLayout>

                <!-- Výběr nomogramu -->
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:text="@string/select_nomogram"
                    android:textColor="@android:color/white"
                    android:textSize="16sp"
                    android:textStyle="bold" />

                <RadioGroup
                    android:id="@+id/radioGroup_nomograms"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:orientation="vertical">

                    <RadioButton
                        android:id="@+id/radio_nomogram_1"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textColor="@android:color/white" />

                    <ImageView
                        android:id="@+id/imageView_nomogram_1"
                        android:layout_width="match_parent"
                        android:layout_height="200dp"
                        android:layout_marginTop="8dp"
                        android:scaleType="centerInside"
                        android:visibility="gone"
                        android:contentDescription="@string/nomogram_image_desc" />

                    <RadioButton
                        android:id="@+id/radio_nomogram_2"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp"
                        android:textColor="@android:color/white" />

                    <ImageView
                        android:id="@+id/imageView_nomogram_2"
                        android:layout_width="match_parent"
                        android:layout_height="200dp"
                        android:layout_marginTop="8dp"
                        android:scaleType="centerInside"
                        android:visibility="gone"
                        android:contentDescription="@string/nomogram_image_desc" />

                </RadioGroup>

                <!-- Vstupní parametry -->
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="24dp"
                    android:text="@string/input_parameters"
                    android:textColor="@android:color/white"
                    android:textSize="16sp"
                    android:textStyle="bold" />

                <!-- Tloušťka t -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:orientation="horizontal"
                    android:gravity="center_vertical">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/thickness_t"
                        android:textColor="@android:color/white"
                        android:textSize="14sp" />

                    <EditText
                        android:id="@+id/editText_t"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:hint="t"
                        android:inputType="numberDecimal"
                        android:textColor="@android:color/white"
                        android:textColorHint="@android:color/white" />

                </LinearLayout>

                <!-- Vnější průměr De -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:orientation="horizontal"
                    android:gravity="center_vertical">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/outer_diameter_de"
                        android:textColor="@android:color/white"
                        android:textSize="14sp" />

                    <EditText
                        android:id="@+id/editText_de"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:hint="De"
                        android:inputType="numberDecimal"
                        android:textColor="@android:color/white"
                        android:textColorHint="@android:color/white" />

                </LinearLayout>

                <!-- Vzdálenost -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:orientation="horizontal"
                    android:gravity="center_vertical">

                    <TextView
                        android:id="@+id/textView_distance_label"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/distance_f"
                        android:textColor="@android:color/white"
                        android:textSize="14sp" />

                    <EditText
                        android:id="@+id/editText_distance"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:hint="f"
                        android:inputType="numberDecimal"
                        android:textColor="@android:color/white"
                        android:textColorHint="@android:color/white" />

                </LinearLayout>

                <!-- Výsledek -->
                <TextView
                    android:id="@+id/textView_result"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="24dp"
                    android:gravity="center"
                    android:text=""
                    android:textColor="@color/result_text"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:visibility="visible" />

            </LinearLayout>

        </androidx.cardview.widget.CardView>

    </LinearLayout>

</ScrollView>