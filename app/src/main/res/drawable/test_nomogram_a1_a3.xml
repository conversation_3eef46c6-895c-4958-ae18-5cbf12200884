<?xml version="1.0" encoding="utf-8"?>
<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="200dp"
    android:height="150dp"
    android:viewportWidth="200"
    android:viewportHeight="150">
    
    <!-- Background -->
    <path
        android:pathData="M0,0 L200,0 L200,150 L0,150 Z"
        android:fillColor="#E0E0E0" />
    
    <!-- Title text area -->
    <path
        android:pathData="M10,10 L190,10 L190,30 L10,30 Z"
        android:fillColor="#FFFFFF"
        android:strokeColor="#333333"
        android:strokeWidth="1" />
    
    <!-- Grid -->
    <path
        android:pathData="M20,40 L20,140"
        android:strokeColor="#CCCCCC"
        android:strokeWidth="1" />
    <path
        android:pathData="M180,40 L180,140"
        android:strokeColor="#CCCCCC"
        android:strokeWidth="1" />
    <path
        android:pathData="M20,40 L180,40"
        android:strokeColor="#CCCCCC"
        android:strokeWidth="1" />
    <path
        android:pathData="M20,140 L180,140"
        android:strokeColor="#CCCCCC"
        android:strokeWidth="1" />
    
    <!-- Sample curve -->
    <path
        android:pathData="M20,120 Q60,100 100,90 Q140,80 180,60"
        android:strokeColor="#FF6600"
        android:strokeWidth="3" />
    
    <!-- Label A1/A3 -->
    <path
        android:pathData="M80,15 L120,15 L120,25 L80,25 Z"
        android:fillColor="#FF6600" />
</vector>
