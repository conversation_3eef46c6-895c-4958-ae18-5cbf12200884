package com.example.stopky.ui.minimumnumberofexposures

import android.content.Context
import android.content.SharedPreferences
import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.*
import androidx.fragment.app.Fragment
import com.example.stopky.R

class MinNumOfExposuresFragment : Fragment() {

    private lateinit var switchClass: Switch
    private lateinit var editTextT: EditText
    private lateinit var editTextDe: EditText
    private lateinit var editTextDistance: EditText
    private lateinit var textViewDistanceLabel: TextView
    private lateinit var radioGroupNomograms: RadioGroup
    private lateinit var radioNomogram1: RadioButton
    private lateinit var radioNomogram2: RadioButton
    private lateinit var layoutNomogram1: LinearLayout
    private lateinit var layoutNomogram2: LinearLayout
    private lateinit var imageViewNomogram1: ImageView
    private lateinit var imageViewNomogram2: ImageView
    private lateinit var textViewResult: TextView

    private var nomogramData: NomogramFile? = null
    private var selectedNomogramKey: String? = null
    private var isClassA: Boolean = false
    private lateinit var sharedPreferences: SharedPreferences

    companion object {
        private const val PREFS_NAME = "MinExposuresPrefs"
        private const val KEY_CLASS_A = "class_a"
        private const val KEY_NOMOGRAM = "nomogram"
        private const val KEY_THICKNESS = "thickness"
        private const val KEY_DIAMETER = "diameter"
        private const val KEY_DISTANCE = "distance"
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        val root = inflater.inflate(R.layout.fragment_min_num_exposures, container, false)

        sharedPreferences = requireContext().getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)

        initializeViews(root)
        loadNomogramData()
        setupListeners()
        restoreSettings()

        return root
    }

    private fun initializeViews(root: View) {
        switchClass = root.findViewById(R.id.switch_class)
        editTextT = root.findViewById(R.id.editText_t)
        editTextDe = root.findViewById(R.id.editText_de)
        editTextDistance = root.findViewById(R.id.editText_distance)
        textViewDistanceLabel = root.findViewById(R.id.textView_distance_label)
        radioGroupNomograms = root.findViewById(R.id.radioGroup_nomograms)
        radioNomogram1 = root.findViewById(R.id.radio_nomogram_1)
        radioNomogram2 = root.findViewById(R.id.radio_nomogram_2)
        layoutNomogram1 = root.findViewById(R.id.layout_nomogram_1)
        layoutNomogram2 = root.findViewById(R.id.layout_nomogram_2)
        imageViewNomogram1 = root.findViewById(R.id.imageView_nomogram_1)
        imageViewNomogram2 = root.findViewById(R.id.imageView_nomogram_2)
        textViewResult = root.findViewById(R.id.textView_result)
    }

    private fun loadNomogramData() {
        nomogramData = NomogramLoader.loadNomograms(requireContext())
    }

    private fun setupListeners() {
        // Listener pro přepínač třídy
        switchClass.setOnCheckedChangeListener { _, isChecked ->
            isClassA = isChecked
            updateNomogramOptions()
            saveSettings()
            calculateMinimumExposures()
        }

        // Listener pro RadioGroup - změna nomogramu
        radioGroupNomograms.setOnCheckedChangeListener { _, checkedId ->
            when (checkedId) {
                R.id.radio_nomogram_1 -> {
                    selectedNomogramKey = if (isClassA) "A3" else "A1"
                    updateDistanceLabel(selectedNomogramKey!!)
                    updateNomogramImages()
                }
                R.id.radio_nomogram_2 -> {
                    selectedNomogramKey = if (isClassA) "A4" else "A2"
                    updateDistanceLabel(selectedNomogramKey!!)
                    updateNomogramImages()
                }
            }
            saveSettings()
            calculateMinimumExposures()
        }

        // Text watchers pro automatický výpočet
        val textWatcher = object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
            override fun afterTextChanged(s: Editable?) {
                saveSettings()
                calculateMinimumExposures()
            }
        }

        editTextT.addTextChangedListener(textWatcher)
        editTextDe.addTextChangedListener(textWatcher)
        editTextDistance.addTextChangedListener(textWatcher)
    }

    private fun updateNomogramOptions() {
        if (isClassA) {
            // Třída A - zobrazit A3 a A4
            radioNomogram1.text = "A3 - ${getString(R.string.nomogram_a3_desc)}"
            radioNomogram2.text = "A4 - ${getString(R.string.nomogram_a4_desc)}"
        } else {
            // Třída B - zobrazit A1 a A2
            radioNomogram1.text = "A1 - ${getString(R.string.nomogram_a1_desc)}"
            radioNomogram2.text = "A2 - ${getString(R.string.nomogram_a2_desc)}"
        }

        layoutNomogram1.visibility = View.VISIBLE
        layoutNomogram2.visibility = View.VISIBLE

        // Resetovat výběr nomogramu
        radioGroupNomograms.clearCheck()
        selectedNomogramKey = null
        hideNomogramImages()
    }

    private fun updateDistanceLabel(nomogramKey: String) {
        nomogramData?.get(nomogramKey)?.let { data ->
            val distanceVariable = data.distance_variable
            textViewDistanceLabel.text = when (distanceVariable) {
                "f" -> getString(R.string.distance_f)
                "SFD" -> getString(R.string.distance_sfd)
                else -> "Vzdálenost ($distanceVariable):"
            }
        }
    }

    private fun updateNomogramImages() {
        hideNomogramImages()

        when (selectedNomogramKey) {
            "A1", "A3" -> {
                // Pro A1 a A3 zobrazit obrázek v prvním ImageView
                imageViewNomogram1.setImageResource(R.drawable.a1_a3)
                imageViewNomogram1.visibility = View.VISIBLE
            }
            "A2", "A4" -> {
                // Pro A2 a A4 zobrazit obrázek v druhém ImageView
                imageViewNomogram2.setImageResource(R.drawable.a2_a4)
                imageViewNomogram2.visibility = View.VISIBLE
            }
        }
    }

    private fun hideNomogramImages() {
        imageViewNomogram1.visibility = View.GONE
        imageViewNomogram2.visibility = View.GONE
    }

    private fun saveSettings() {
        with(sharedPreferences.edit()) {
            putBoolean(KEY_CLASS_A, isClassA)
            putString(KEY_NOMOGRAM, selectedNomogramKey)
            putString(KEY_THICKNESS, editTextT.text.toString())
            putString(KEY_DIAMETER, editTextDe.text.toString())
            putString(KEY_DISTANCE, editTextDistance.text.toString())
            apply()
        }
    }

    private fun restoreSettings() {
        isClassA = sharedPreferences.getBoolean(KEY_CLASS_A, false)
        switchClass.isChecked = isClassA

        editTextT.setText(sharedPreferences.getString(KEY_THICKNESS, ""))
        editTextDe.setText(sharedPreferences.getString(KEY_DIAMETER, ""))
        editTextDistance.setText(sharedPreferences.getString(KEY_DISTANCE, ""))

        updateNomogramOptions()

        val savedNomogram = sharedPreferences.getString(KEY_NOMOGRAM, null)
        if (savedNomogram != null) {
            selectedNomogramKey = savedNomogram
            when (savedNomogram) {
                "A1", "A3" -> radioNomogram1.isChecked = true
                "A2", "A4" -> radioNomogram2.isChecked = true
            }
            updateDistanceLabel(savedNomogram)
            updateNomogramImages()
        }

        calculateMinimumExposures()
    }

    private fun calculateMinimumExposures() {
        // Skrýt výsledek pokud nejsou všechny hodnoty vyplněny
        val tText = editTextT.text.toString().trim()
        val deText = editTextDe.text.toString().trim()
        val distanceText = editTextDistance.text.toString().trim()

        Log.d("MinExposures", "Calculate called: t='$tText', de='$deText', distance='$distanceText', nomogram='$selectedNomogramKey'")

        if (tText.isEmpty() || deText.isEmpty() || distanceText.isEmpty() || selectedNomogramKey == null) {
            Log.d("MinExposures", "Missing values, hiding result")
            textViewResult.visibility = View.GONE
            return
        }

        try {
            val t = tText.toFloat()
            val de = deText.toFloat()
            val distance = distanceText.toFloat()

            // Získat data pro zvolený nomogram
            val selectedNomogramData = nomogramData?.get(selectedNomogramKey!!)
            if (selectedNomogramData == null) {
                showError(getString(R.string.error_calculation_failed))
                return
            }

            // Vytvořit kalkulátor a vypočítat N
            val calculator = NomogramCalculator(selectedNomogramData)
            val result = calculator.calculateN(t, de, distance)

            if (result == -1) {
                showError(getString(R.string.error_calculation_failed))
            } else {
                showResult(result)
            }

        } catch (e: NumberFormatException) {
            textViewResult.visibility = View.GONE
        } catch (e: Exception) {
            showError(getString(R.string.error_calculation_failed))
        }
    }

    private fun showResult(result: Int) {
        val resultText = getString(R.string.result_min_exposures, result)
        Log.d("MinExposures", "Showing result: $resultText")
        textViewResult.text = resultText
        textViewResult.visibility = View.VISIBLE
    }

    private fun showError(message: String) {
        Log.d("MinExposures", "Showing error: $message")
        textViewResult.text = message
        textViewResult.visibility = View.VISIBLE
    }
}